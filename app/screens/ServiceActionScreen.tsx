/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useState, useMemo } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  Screen,
  Text,
  Header,
  ProgressReport,
  SneLeRecu,
  PortalView,
  Icon,
  BuyAirtime,
} from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import type { SNELRequest } from "@/store/ServicesStore"

interface ServiceActionScreenProps extends AppStackScreenProps<"ServiceAction"> {}

type StatusFilter = "all" | "pending" | "completed" | "failed"
type TabType = "facture" | "meter"

export const ServiceActionScreen: FC<ServiceActionScreenProps> = ({ navigation }) => {
  const [selectedStatus, setSelectedStatus] = useState<StatusFilter>("all")
  const [selectedRequest, setSelectedRequest] = useState<SNELRequest | null>(null)
  const [isPortalVisible, setIsPortalVisible] = useState(false)
  const [activeTab, setActiveTab] = useState<TabType>("facture")

  const {
    services: { getRequestsByStatus, getMeterDataByRequest, snelHistory },
  } = useStores()

  const filteredRequests = useMemo(() => {
    if (!Array.isArray(snelHistory)) return []

    if (selectedStatus === "all") {
      return snelHistory.flatMap((meter) => meter.requests)
    }
    return getRequestsByStatus(selectedStatus as "pending" | "completed" | "failed")
  }, [selectedStatus, snelHistory, getRequestsByStatus])

  const handleRequestPress = (request: SNELRequest) => {
    setSelectedRequest(request)
    setIsPortalVisible(true)
  }

  const statusButtons: { label: string; value: StatusFilter }[] = [
    { label: "Tous", value: "all" },
    { label: "En cours", value: "pending" },
    { label: "Complétés", value: "completed" },
    { label: "Échoués", value: "failed" },
  ]

  const renderFactureList = () => (
    <>
      <View style={$filterContainer}>
        {statusButtons.map(({ label, value }) => (
          <TouchableOpacity
            key={value}
            style={[$filterButton, selectedStatus === value && $filterButtonActive]}
            onPress={() => setSelectedStatus(value)}
          >
            <Text style={[$filterButtonText, selectedStatus === value && $filterButtonTextActive]}>
              {label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Services List */}
      <View style={$listContainer}>
        {filteredRequests.map((request) => {
          const meterData = getMeterDataByRequest(request)
          return (
            <ProgressReport
              key={request.id}
              progress={
                request.status === "completed" ? 100 : request.status === "pending" ? 50 : 0
              }
              status={request.status}
              description={
                request.status === "completed"
                  ? `${meterData?.name}`
                  : `Achat du SNEL CASH POWER de ${request.amount}`
              }
              transactionId={meterData?.meter || request.saved_meter}
              onPress={() => handleRequestPress(request)}
            />
          )
        })}
      </View>
    </>
  )
  const renderMeterList = () => {
    return (
      <View style={$contentContainer}>
        <Text style={$sectionTitle}>Mes compteurs</Text>
        <Text style={$emptyText}>Aucun compteur trouvé</Text>
      </View>
    )
  }

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title="Services"
        backgroundColor={colors.palette.neutral100}
      />

      <Screen style={$root} preset="fixed" statusBarStyle="dark">
        <View style={$tabContainer}>
          <TouchableOpacity
            style={[$tabButton, activeTab === "facture" && $activeTabButton]}
            onPress={() => setActiveTab("facture")}
          >
            <View style={$tabContent}>
              <Icon
                icon="utilities"
                color={
                  activeTab === "facture" ? colors.palette.neutral900 : colors.palette.accent300
                }
                size={24}
              />
              <Text style={[$tabText, activeTab === "facture" && $activeTabText]}>Factures</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[$tabButton, activeTab === "meter" && $activeTabButton]}
            onPress={() => setActiveTab("meter")}
          >
            <View style={$tabContent}>
              <Icon
                icon="cashpower"
                color={activeTab === "meter" ? colors.palette.neutral900 : colors.palette.accent300}
                size={24}
              />
              <Text style={[$tabText, activeTab === "meter" && $activeTabText]}>Mes Compteurs</Text>
            </View>
          </TouchableOpacity>
        </View>

        {activeTab === "facture" && renderFactureList()}
        {activeTab === "meter" && renderMeterList()}
      </Screen>

      <PortalView
        visible={isPortalVisible}
        icon="backicon"
        title="Détails de la transaction"
        onClose={() => {
          setIsPortalVisible(false)
          setSelectedRequest(null)
        }}
      >
        {selectedRequest && (
          <SneLeRecu
            moredata={getMeterDataByRequest(selectedRequest)}
            meterNumber={getMeterDataByRequest(selectedRequest)?.meter}
            amount={selectedRequest.amount}
            metername={getMeterDataByRequest(selectedRequest)?.name}
            totalValue={selectedRequest.amount}
            currency={selectedRequest.currency.code || ""}
            date={selectedRequest.created_at}
            status={selectedRequest.status}
            // onCancelRequest
          />
        )}
      </PortalView>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $contentContainer: ViewStyle = {
  flex: 1,
  padding: spacing.md,
}

const $filterContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.lg,
  gap: spacing.xs,
}

const $tabContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  // paddingVertical: 10,
  // borderBottomWidth: 1,
  // borderBottomColor: colors.palette.neutral200,
}

const $filterButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.xs,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  alignItems: "center",
  justifyContent: "center",
}

const $filterButtonActive: ViewStyle = {
  backgroundColor: colors.palette.primary600,
}

const $filterButtonText: TextStyle = {
  fontSize: 12,
  color: colors.text,
}

const $filterButtonTextActive: TextStyle = {
  color: colors.palette.neutral100,
}

const $listContainer: ViewStyle = {
  padding: spacing.sm,
  gap: spacing.xs,
}

const $tabButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  borderBottomWidth: 2,
  borderBottomColor: colors.palette.neutral300,
}

const $activeTabButton: ViewStyle = {
  borderBottomColor: colors.palette.neutral900,
}

const $tabContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.xs,
}

const $tabText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $activeTabText: TextStyle = {
  color: colors.palette.neutral900,
  fontWeight: "bold",
}

const $sectionTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  marginBottom: spacing.md,
}

const $emptyText: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral500,
  textAlign: "center",
  marginTop: spacing.xl,
}
