import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface InfoScreenProps extends AppStackScreenProps<"Info"> {}


export const InfoScreen: FC<InfoScreenProps> = () => {

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="info" />
    </Screen>
  )

}

const $root: ViewStyle = {
  flex: 1,
}
