/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable import/no-unresolved */
import { FC, useState } from "react"
import { ViewStyle, View, Modal, TouchableOpacity, StyleSheet, Dimensions, TextStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  ActionIcon,
  BannerHero,
  Beneficiaires,
  Header,
  PayBills,
  PortalView,
  Screen,
  Text,
  Icon,
  Button,
} from "@/components"
import { colors } from "@/theme"
import { PayerScreenMenu } from "@/utils/Menus"
import React from "react"
// import { useNavigation } from "@react-navigation/native"

interface PayScreenProps extends AppStackScreenProps<"Pay"> {}
const bgimge = require("../../../../assets/images/banners/payBanner.png")

export const PayScreen: FC<PayScreenProps> = ({ navigation }) => {
  const [modalState, setModalState] = useState({
    visible: false,
    content: null as React.ReactNode | null,
    iscontent: "",
  })

  // State for the coming soon modal
  const [comingSoonModal, setComingSoonModal] = useState({
    visible: false,
    serviceName: "",
    serviceIcon: "",
    description: "",
  })

  const openModal = (title: string, content: React.ReactNode) => {
    setModalState({ visible: true, iscontent: title, content })
  }

  const closeModal = () => {
    setModalState({ visible: false, iscontent: "", content: null })
  }

  // Function to open the coming soon modal
  const openComingSoonModal = (serviceName: string, serviceIcon: string, description?: string) => {
    setComingSoonModal({
      visible: true,
      serviceName,
      serviceIcon,
      description: description || "",
    })
  }

  // Function to close the coming soon modal
  const closeComingSoonModal = () => {
    setComingSoonModal({
      visible: false,
      serviceName: "",
      serviceIcon: "",
      description: "",
    })
  }

  const handleServicePress = (service: any) => {
    console.log("service", service)
    switch (service.serviceType) {
      case "Beneficiary":
        // openModal("Payez un bénéficiaire", <Beneficiaires />)
        navigation.navigate("Beneficiary")
        break
      case "ElectricityBillsPayment":
        // Handle regular transfer
        openModal("Payer vos factures d'électricité", <PayBills navigation={navigation} />)
        break
      case "TransferScantoPay":
        openModal("Lipa na Fedha", <></>)
        break
      case "WaterBillsPayment":
        // Show coming soon modal for water bills payment
        openComingSoonModal(
          "REGIDESO",
          "water",
          "Le service de paiement des factures d'eau sera bientôt disponible. Nous travaillons pour vous offrir une expérience de paiement simple et sécurisée."
        )
        break
      case "Lipatransport":
        // Show coming soon modal for transport payment
        openComingSoonModal(
          "Transport",
          "taxi",
          "Le service de paiement de transport sera bientôt disponible. Préparez-vous à voyager en toute simplicité avec Fedha!"
        )
        break
      default:
        console.log("Service not implemented:", service.serviceType)
    }
  }
  return (
    <>
      <Header
        leftIcon={"backicon"}
        onLeftPress={navigation.goBack}
        title="Payer "
        backgroundColor="white"
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        <BannerHero
          backgroundImage={bgimge}
          textColor={colors.palette.neutral800}
          title={`Payer`}
          subtitle={`La simplicité à${"\n"}portée de main`}
        />
        <View style={$ServiceContainer}>
          {PayerScreenMenu.map((item, index) => {
            return (
              <ActionIcon
                key={index}
                icon={item.iconName}
                title={item.serviceName}
                onPress={() => handleServicePress(item)}
              />
            )
          })}
        </View>
      </Screen>
      <PortalView
        visible={modalState.visible}
        // status={loading}
        icon={"backicon"}
        title={modalState.iscontent}
        onClose={closeModal}
      >
        <View>{modalState.content}</View>
      </PortalView>

      {/* Coming Soon Modal */}
      <Modal visible={comingSoonModal.visible} transparent animationType="fade">
        <View style={$modalOverlay}>
          <View style={$modalContainer}>
            {/* Close button */}
            <TouchableOpacity style={$closeButton} onPress={closeComingSoonModal}>
              <Icon icon="x" size={24} color={colors.palette.neutral600} />
            </TouchableOpacity>

            {/* Service icon */}
            <View style={$iconContainer}>
              {comingSoonModal.serviceIcon ? (
                <Icon
                  icon={comingSoonModal.serviceIcon as any}
                  size={40}
                  color={colors.palette.primary500}
                />
              ) : (
                <Icon icon="info" size={40} color={colors.palette.primary500} />
              )}
            </View>

            {/* Title */}
            <Text preset="heading" style={$title}>
              {comingSoonModal.serviceName}
            </Text>

            {/* Coming soon text */}
            <Text preset="subheading" style={$comingSoonText}>
              Bientôt disponible
            </Text>

            {/* Description */}
            <Text style={$description}>
              {comingSoonModal.description ||
                `Nous travaillons actuellement sur cette fonctionnalité pour vous offrir une expérience exceptionnelle. Restez à l'écoute!`}
            </Text>

            {/* Animation placeholder */}
            <View style={$animationContainer}>
              <View style={$gifPlaceholder}>
                <Icon icon="loading" size={50} color={colors.palette.primary500} />
                <Text style={$placeholderText}>En préparation...</Text>
              </View>
            </View>

            {/* Button */}
            <Button
              text="J'ai compris"
              preset="reversed"
              onPress={closeComingSoonModal}
              style={$button}
            />
          </View>
        </View>
      </Modal>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $ServiceContainer: ViewStyle = {
  paddingTop: 25,
  flex: 1,
  flexDirection: "row",
  flexWrap: "wrap",
  alignItems: "flex-start",
  justifyContent: "flex-start",
  paddingHorizontal: 8,
  gap: 8,
}

// Coming Soon Modal Styles
const { width } = Dimensions.get("window")

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContainer: ViewStyle = {
  width: width * 0.85,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: 20,
  alignItems: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
}

const $closeButton: ViewStyle = {
  position: "absolute",
  top: 10,
  right: 10,
  padding: 5,
  zIndex: 1,
}

const $iconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginTop: 20,
  marginBottom: 15,
}

const $title: TextStyle = {
  fontSize: 22,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  textAlign: "center",
  marginBottom: 5,
}

const $comingSoonText: TextStyle = {
  fontSize: 18,
  color: colors.palette.primary500,
  fontWeight: "600",
  textAlign: "center",
  marginBottom: 15,
}

const $description: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginBottom: 20,
  paddingHorizontal: 10,
}

const $animationContainer: ViewStyle = {
  height: 150,
  width: "100%",
  marginBottom: 20,
  justifyContent: "center",
  alignItems: "center",
}

const $gifPlaceholder: ViewStyle = {
  width: 200,
  height: 150,
  backgroundColor: colors.palette.primary100,
  borderRadius: 12,
  justifyContent: "center",
  alignItems: "center",
}

const $placeholderText: TextStyle = {
  marginTop: 10,
  color: colors.palette.primary500,
  fontWeight: "600",
}

const $button: ViewStyle = {
  minWidth: 200,
  marginTop: 10,
}
