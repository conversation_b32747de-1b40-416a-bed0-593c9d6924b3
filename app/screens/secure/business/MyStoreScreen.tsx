/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useState, useCallback } from "react"
import { View, StyleSheet, TouchableOpacity, FlatList, Modal } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  ActionIcon,
  ActionInfoBox,
  Bheader,
  Button,
  Header,
  Icon,
  Screen,
  StoreWidgetCard,
  Text,
} from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store"
import { MystoreFeatures } from "@/utils/Menus"
import React from "react"
// Import an icon library (e.g., MaterialCommunityIcons) if available and configured
// import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// --- Types ---
interface Product {
  id: string
  name: string
  totalSold: number
}

type Period = "Quotidien" | "Hebdomadaire" | "Mensuel"

interface MyStoreScreenProps extends AppStackScreenProps<"MyStore"> {}

// --- Mock Data (Replace with actual data fetching logic) ---

// const mockProfit = 250000 // Example Profit

// const mockProducts: Record<Period, Product[]> = {
//   Quotidien: [
//     { id: "p1", name: "Pain Ngoma", totalSold: 55 },
//     { id: "p2", name: "Savon Le Chat", totalSold: 42 },
//     { id: "p3", name: "Sucre Kwilu", totalSold: 30 },
//     { id: "p4", name: "Eau Vive 1.5L", totalSold: 25 },
//   ],
//   Hebdomadaire: [
//     { id: "p1", name: "Pain Ngoma", totalSold: 385 },
//     { id: "p5", name: "Riz Lion", totalSold: 350 },
//     { id: "p2", name: "Savon Le Chat", totalSold: 294 },
//     { id: "p3", name: "Sucre Kwilu", totalSold: 210 },
//     { id: "p6", name: "Huile Simba", totalSold: 190 },
//   ],
//   Mensuel: [
//     { id: "p1", name: "Pain Ngoma", totalSold: 1540 },
//     { id: "p5", name: "Riz Lion", totalSold: 1400 },
//     { id: "p2", name: "Savon Le Chat", totalSold: 1176 },
//     { id: "p3", name: "Sucre Kwilu", totalSold: 840 },
//     { id: "p6", name: "Huile Simba", totalSold: 760 },
//     { id: "p7", name: "Sel Diana", totalSold: 600 },
//   ],
// }

// const mockPredictions = {
//   targetAmount: 950000, // Example Target CDF
//   traffic: "High", // Example Traffic String
//   trafficChange: "+5%", // Example change indicator
// }
// --- End Mock Data ---

export const MyStoreScreen: FC<MyStoreScreenProps> = ({ navigation }) => {
  const {
    // auth: { user },
    business: { businesses },
  } = useStores()
  const [messageNotifications, setMessageNotifications] = useState(1)

  const mybusiness = businesses[0]
  // console.log('s',mybusiness)
  // const [profit] = useState(mockProfit)
  // const [predictions] = useState(mockPredictions)
  // const [selectedPeriod, setSelectedPeriod] = useState<Period>("Quotidien")
  // const [topProducts, setTopProducts] = useState<Product[]>(mockProducts.Quotidien)
  // Modal states
  // const [isPeriodModalVisible, setIsPeriodModalVisible] = useState(false)

  // const handlePeriodChange = useCallback((period: Period) => {
  //   setSelectedPeriod(period)
  //   // TODO: In a real app, you might trigger a data fetch here based on the period
  //   setTopProducts(mockProducts[period])
  // }, [])

  // --- Render ---
  // Period selector dropdown
  // const renderPeriodSelector = () => {
  //   return (
  //     <TouchableOpacity
  //       style={styles.periodSelectorButton}
  //       onPress={() => setIsPeriodModalVisible(true)}
  //     >
  //       <Text style={styles.periodSelectorText}>
  //         {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)}
  //       </Text>
  //       <Icon icon="caretRight" size={16} color="#333" />
  //     </TouchableOpacity>
  //   )
  // }

  // console.log("Businesses:", businesses)

  // Period selector modal
  // const renderPeriodModal = () => {
  //   const periods: Period[] = ["Quotidien", "Hebdomadaire", "Mensuel"]

  //   return (
  //     <Modal
  //       visible={isPeriodModalVisible}
  //       transparent={true}
  //       animationType="fade"
  //       onRequestClose={() => setIsPeriodModalVisible(false)}
  //     >
  //       <TouchableOpacity
  //         style={styles.modalOverlay}
  //         activeOpacity={1}
  //         onPress={() => setIsPeriodModalVisible(false)}
  //       >
  //         <View style={styles.periodModalContent}>
  //           {periods.map((period) => (
  //             <TouchableOpacity
  //               key={period}
  //               style={[
  //                 styles.periodOption,
  //                 selectedPeriod === period && styles.periodOptionSelected,
  //               ]}
  //               onPress={() => {
  //                 handlePeriodChange(period)
  //                 setIsPeriodModalVisible(false)
  //               }}
  //             >
  //               <Text
  //                 style={[
  //                   styles.periodOptionText,
  //                   selectedPeriod === period && styles.periodOptionTextSelected,
  //                 ]}
  //               >
  //                 {period.charAt(0).toUpperCase() + period.slice(1)}
  //               </Text>
  //             </TouchableOpacity>
  //           ))}
  //         </View>
  //       </TouchableOpacity>
  //     </Modal>
  //   )
  // }

  // Render item for FlatList
  // const renderProductItem = ({ item }: { item: Product }) => (
  //   <View style={styles.productItem}>
  //     <Text style={styles.productName}>{item.name}</Text>
  //     <Text style={styles.productSold}>{item.totalSold} Sold</Text>
  //   </View>
  // )

  const handleServicePress = (service: any) => {
    switch (service.serviceType) {
      case "Produits":
        navigation.navigate("MyShelf")
        break
      case "Personnel":
        navigation.navigate("MyStaff")
        break
      case "Factures":
        navigation.navigate("Invoice")
        break
      case "Message":
        navigation.navigate("Message")
        break
      case "Rapports":
        navigation.navigate("BusinessReports")
        break
      case "Elimu":
        navigation.navigate("Elimu")
        break
      default:
      // console.log("Service not implemented:", service.serviceType)
    }
  }

  return (
    <>
      <Screen
        preset="scroll"
        style={styles.screen}
        safeAreaEdges={["top", "bottom"]}
        statusBarStyle="dark"
      >
        <View style={styles.screenContainer}>
          <StoreWidgetCard
            storeName={mybusiness?.name}
            storeType={mybusiness?.wallet.pochi}
            iconName={mybusiness?.image_logo ? { uri: mybusiness?.image_logo } : mybusiness?.name}
            // iconColor={brandColors.coral} // Use coral for this icon
            // onPress={() => console.log("Navigate to Store ")}
          />
          <View style={styles.contentSections}>
            {/* {MystoreFeatures.map((item, index) => {
              return (
                <ActionIcon
                  key={index}
                  icon={item.iconName}
                  title={item.serviceName}
                  onPress={() => handleServicePress(item)}
                />
              )
            })} */}
            {MystoreFeatures.map((item, index) => {
              let needAttention = null

              if (item.serviceName === "Message" && messageNotifications > 0) {
                needAttention = messageNotifications
              }

              return (
                <ActionIcon
                  key={index}
                  icon={item.iconName}
                  title={item.serviceName}
                  needAttention={needAttention}
                  onPress={() => handleServicePress(item)}
                />
              )
            })}
            {/* <ActionInfoBox
              onPress={() => console.log("Navigate to Add Product")}
              style={styles.cardBase}
            >
              <View>
                <Text style={styles.title}>Products</Text>
                <Text style={styles.subtitle}>Add your first product</Text>
              </View>
              <View style={styles.iconContainer}>
                <Icon icon="plus" size={24} color="#9ca3af" />
              </View>
            </ActionInfoBox> */}
            {/* <View style={styles.card}>
              <View style={styles.cardRow}>
                <Text style={styles.cardLabel}>Transaction du jour</Text>
                <Text style={styles.cardValueProfit}>
                  {profit.toLocaleString("fr-CD", { style: "currency", currency: "CDF" })}
                </Text>
              </View>
            </View> */}

            {/* <View style={styles.card}>
              <View style={styles.cardHeader}>
                <Text style={styles.cardTitle}>Les plus vendus</Text>
                {renderPeriodSelector()}
              </View>
              <FlatList
                data={topProducts}
                renderItem={renderProductItem}
                keyExtractor={(item) => item.id}
                scrollEnabled={false}
                ItemSeparatorComponent={() => <View style={styles.productSeparator} />}
              />
            </View> */}

            {/* <View style={styles.card}>
              <View style={styles.cardRow}>
                <Text style={styles.cardLabel}>Target Amount (Day)</Text>
                <Text style={styles.cardValue}>
                  {predictions.targetAmount.toLocaleString("fr-CD", {
                    style: "currency",
                    currency: "CDF",
                  })}
                </Text>
              </View>
              <View style={styles.cardRow}>
                <Text style={styles.cardLabel}>Sales Traffic</Text>
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <Text
                    style={[
                      styles.cardValue,
                      {
                        color:
                          predictions.traffic === "High"
                            ? colors.palette.primary500
                            : predictions.traffic === "Low"
                              ? "#dc3545"
                              : "#E0E0E0",
                      },
                    ]}
                  >
                    {predictions.traffic}
                  </Text>
                  {predictions.trafficChange && (
                    <Text
                      style={[
                        styles.trafficChange,
                        {
                          color: predictions.trafficChange.startsWith("+")
                            ? colors.palette.primary500
                            : "#dc3545",
                        },
                      ]}
                    >
                      ({predictions.trafficChange})
                    </Text>
                  )}
                </View>
              </View>
            </View> */}
          </View>
        </View>

        {/* Period Modal */}
        {/* {renderPeriodModal()} */}
      </Screen>
    </>
  )
}

// --- Styles ---
const styles = StyleSheet.create({
  // Screen styles
  screen: {
    flex: 1,
    // backgroundColor: "#FFFFFF",
  },
  scrollView: {
    // backgroundColor: "#1C1C1E", // Dark background for the whole scroll area
  },
  scrollContentContainer: {
    paddingBottom: 30, // Ensure space at the bottom
  },
  screenContainer: {
    flex: 1,
    // backgroundColor: "#FFF", // Very dark grey/black background
    paddingHorizontal: 15,
    paddingTop: 20,
  },
  // Business Header
  businessHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 15,
    marginBottom: 10,
  },
  businessName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333333", // Dark text for light theme
  },
  businessType: {
    fontSize: 13,
    color: "#666666", // Medium grey text
    marginTop: 2,
  },

  cardBase: {
    minHeight: 100, // Give it some minimum height
    position: "relative", // Needed for absolute positioning of the icon
  },
  title: {
    fontWeight: "500", // font-medium equivalent
    color: "#1f2937", // gray-800 equivalent
    marginBottom: 4, // mb-1 equivalent (adjust value as needed)
    fontSize: 16, // Example size
  },
  subtitle: {
    fontSize: 14, // text-sm equivalent
    color: "#6b7280", // gray-500 equivalent
  },
  iconContainer: {
    position: "absolute", // Position relative to the parent FeaturedBox
    bottom: 12, // Adjust as needed
    left: 12, // Adjust as needed
  },

  // Period Selector Styles
  periodSelectorButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: colors.palette.accent400,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 10,
    marginBottom: 9,
  },
  periodSelectorText: {
    fontSize: 16,
    color: "#333333",
    fontWeight: "500",
  },
  // Period Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  periodModalContent: {
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 15,
    width: "80%",
    maxWidth: 300,
  },
  periodOption: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#EEEEEE",
  },
  periodOptionSelected: {
    backgroundColor: "#F0F0F0",
  },
  periodOptionText: {
    fontSize: 16,
    color: "#333333",
  },
  periodOptionTextSelected: {
    fontWeight: "bold",
    color: "#000000",
  },
  viewAllText: {
    fontSize: 14,
    color: "#0A84FF", // Blue color for links (adjust as needed)
    fontWeight: "500",
  },
  // Content Sections Area
  contentSections: {
    paddingTop: spacing.xl,
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "flex-start",
    justifyContent: "space-between",
    // paddingHorizontal: 5,
    gap: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: colors.palette.neutral900, // White title
    marginBottom: 15,
    paddingHorizontal: 5, // Align with cards
  },
  // Card Styling (like image's account cards)
  card: {
    backgroundColor: colors.palette.neutral300, // Slightly lighter dark grey for cards
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 15,
    marginBottom: 12, // Space between cards
  },
  cardHeader: {
    // For cards with title and controls (like Products)
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#3A3A3C", // Subtle border
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.palette.neutral800, // Light text for card titles
    // marginBottom: 10, // Only if no cardHeader
  },
  cardRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 6, // Add some vertical padding within rows
  },
  cardLabel: {
    fontSize: 14,
    color: colors.palette.neutral800, // Dim grey for labels
  },
  cardValue: {
    fontSize: 14,
    fontWeight: "600",
    color: colors.palette.neutral900, // Light text for values
  },
  cardValueProfit: {
    // Specific style for profit value
    fontSize: 14,
    fontWeight: "600",
    color: colors.palette.primary500, // Bright green color
  },
  // Period Selector (Custom Segmented Control)
  periodSelectorContainer: {
    flexDirection: "row",
    backgroundColor: "#3A3A3C", // Darker background for the selector group
    borderRadius: 7,
    padding: 2,
  },
  periodButton: {
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 5,
    flex: 1, // Distribute space equally
    alignItems: "center",
  },
  periodButtonSelected: {
    backgroundColor: colors.palette.primary500, // Background for selected segment
  },
  periodButtonText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#E0E0E0",
  },
  periodButtonTextSelected: {
    color: "#FFFFFF", // White text for selected segment
  },
  // Product List Item
  productItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
  },
  productName: {
    fontSize: 14,
    color: colors.palette.neutral800,
  },
  productSold: {
    fontSize: 13,
    color: colors.palette.terciary100,
  },
  productSeparator: {
    height: 1,
    backgroundColor: colors.palette.neutral400, // Separator line color
    marginHorizontal: -5, // Adjust if needed based on card padding
  },
  // Traffic Indicator
  trafficChange: {
    fontSize: 12,
    marginLeft: 5,
  },
})
