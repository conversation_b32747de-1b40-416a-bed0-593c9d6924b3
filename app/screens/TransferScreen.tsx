/* eslint-disable react-native/no-inline-styles */
/* eslint-disable prettier/prettier */
/* eslint-disable import/no-unresolved */
import { FC, useState } from "react"
import { ViewStyle, View } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { ActionIcon, BannerHero, Header, PortalView, Screen, TransferExternal, TransferFedhaPochi } from "@/components"
import { colors } from "@/theme"
import { TransferScreenMenu } from "@/utils/Menus"
import React from "react"

interface TransferScreenProps extends AppStackScreenProps<"Transfer"> {}

const bgimge = require("../../assets/images/banners/BackgroundO.png")
export const TransferScreen: FC<TransferScreenProps> = ({ navigation }) => {

  const [modalState, setModalState] = useState({
    visible: false,
    content: null as React.ReactNode | null,
    iscontent: "",
  })

  const openModal = (title: string, content: React.ReactNode) => {
    setModalState({ visible: true, iscontent: title, content })
  }

  const closeModal = () => {
    setModalState({ visible: false, iscontent: "", content: null })
  }

  const handleServicePress = (service: any) => {
    switch (service.serviceType) {
      case "ExternalWallet":
        openModal("Transfert vers eWallet", <TransferExternal />)
        break
      case "TRANSFER":
        // Handle regular transfer
        openModal("Transfert vers FedhaPochi", <TransferFedhaPochi />)
        break
      case "TransferHistory":
        navigation.navigate("Historic")
        break
      case "FedhaElimu":
        navigation.navigate("ElimuHub", { serviceName: "FedhaTransfer" })
        break
      default:
        console.log("Service not implemented:", service.serviceType)
    }
  }
  return (
    <>
      <Header
        leftIcon={"backicon"}
        onLeftPress={navigation.goBack}
        title="Transférez "
        backgroundColor="white"
      />
      <Screen style={$root} preset="scroll" safeAreaEdges={["bottom"]} statusBarStyle="dark">
        <BannerHero
          backgroundImage={bgimge}
          textColor={colors.palette.neutral100}
          title={`Transfert`}
          subtitle={`Transférez de l’argent ${"\n"} en toute sérénité.`}
        />
        <View style={$ServiceContainer}>
          {TransferScreenMenu.map((item, index) => {
            return (
              <ActionIcon
                key={index}
                icon={item.iconName}
                title={item.serviceName}
                onPress={() => handleServicePress(item)}
              />
            )
          })}
        </View>
      </Screen>
      <PortalView
        visible={modalState.visible}
        // status={loading}
        icon={"backicon"}
        title={modalState.iscontent}
        onClose={closeModal}
      >
        <View>{modalState.content}</View>
      </PortalView>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
}

const $ServiceContainer: ViewStyle = {
  paddingTop: 20,
  flex: 1,
  flexDirection: "row",
  flexWrap: "wrap",
  alignItems: "flex-start", // Aligns items at the start of each row
  // justifyContent: "space-between", // Aligns items from the start of the container
  paddingHorizontal: 8, // Add some padding to prevent items from touching edges
  gap: 8,
}
